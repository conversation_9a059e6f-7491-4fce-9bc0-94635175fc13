# RSL RL
Fast and simple implementation of RL algorithms, designed to run fully on GPU.
This code is an evolution of `rl-pytorch` provided with NVIDIA's <PERSON>.

Only PPO is implemented for now. More algorithms will be added later.
Contributions are welcome.

## Setup

```
git clone https://github.com/leggedrobotics/rsl_rl
cd rsl_rl
pip install -e .
```

**Maintainer**: <PERSON><PERSON>  
**Affiliation**: Robotic Systems Lab, ETH Zurich & NVIDIA
**Contact**: <EMAIL>  



