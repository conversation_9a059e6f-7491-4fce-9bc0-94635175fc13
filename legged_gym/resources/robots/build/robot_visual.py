import time

import mujoco
import mujoco.viewer

m = mujoco.MjModel.from_xml_path('/home/<USER>/Documents/PhybotSofware/RobotModel/phybot/urdf/phybot_v6_floating_whole.xml')
d = mujoco.MjData(m)

with mujoco.viewer.launch_passive(m, d) as viewer:
  # Close the viewer automatically after 30 wall-seconds.
  start = time.time()
  while viewer.is_running() and time.time() - start < 60:
    step_start = time.time()
    print(d.qfrc_actuator)
    # mj_step can be replaced with code that also evaluates
    # a policy and applies a control signal before stepping the physics.
    # d.qpos = 0,0,1,0,0,0,0, -0.2,0,0,0.4,-0.2,0,  -0.2,0,0,0.4,-0.2,0
    # print(m)
    mujoco.mj_step(m, d)

    # Example modification of a viewer option: toggle contact points every two seconds.
    with viewer.lock():
      viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_CONTACTPOINT] = int(d.time % 2)

    # Pick up changes to the physics state, apply perturbations, update options from GUI.
    viewer.sync()

    # Rudimentary time keeping, will drift relative to wall clock.
    time_until_next_step = m.opt.timestep - (time.time() - step_start)
    if time_until_next_step > 0:
      time.sleep(time_until_next_step)