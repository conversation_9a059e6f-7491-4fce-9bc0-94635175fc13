import pinocchio as pin
import numpy as np
import os

# 1. 从指定路径加载 URDF 模型
model_path = "/home/<USER>/Documents/PhybotSofware/RobotModel/phybot_v8/urdf/phybot_v8_26.urdf"  # 替换为你的URDF路径
model = pin.buildModelFromUrdf(model_path)
data = model.createData()

# 2. 指定特定姿态的广义坐标q，赋值如下：
q = np.array([
    -0.822032, -0.150133, -0.374117, 1.37, -0.619452, 0.00128549, 
    -0.822032, 0.150133, 0.374117, 1.37, -0.619452, -0.00128549, 
    0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
])
# 检查q的长度是否与模型自由度匹配
assert q.shape[0] == model.nq, f"q的长度({q.shape[0]})与模型的自由度({model.nq})不匹配"

# 3. 更新动力学数据（前向动力学）
pin.forwardKinematics(model, data, q)
pin.updateFramePlacements(model, data)

# print(pin.__version__)  # 输出版本号
pin.centerOfMass(model, data, q)

# 4. 打印完整模型的质心
print("Full CoM:", data.com[0].T)  # data.com[0] 是全局质心

# 5. 忽略特定刚体（如两条腿）计算部分质心
# 定义要忽略的刚体名称（根据URDF中的link或joint名）
# bodies_to_ignore = []  # 替换为你的腿部刚体名
bodies_to_ignore = [
    "left_hip_roll",
    "left_hip_yaw",
    "left_knee",
    "left_ankle_pitch",
    "left_ankle_roll",
    "left_toe",
    "right_hip_roll",
    "right_hip_yaw",
    "right_knee",
    "right_ankle_pitch",
    "right_ankle_roll",
    "right_toe"
]

total_mass = 0.0
weighted_com = np.zeros(3)

# 遍历所有刚体（跳过世界坐标系 joint_id=0）
for joint_id in range(0, model.njoints):
    body_name = model.names[joint_id]
    
    # 检查是否在忽略列表中
    if any(ignore_name in body_name for ignore_name in bodies_to_ignore):
        continue
    
    # 获取刚体质量和局部质心位置
    mass = model.inertias[joint_id].mass
    body_com_local = model.inertias[joint_id].lever

    print("body_name:", body_name)
    print("mass:", mass)
    print("body_com_local:", body_com_local.T)
    
    # 转换到世界坐标系（需用 forwardKinematics 后的 data.oMi[joint_id]）
    body_com_world = data.oMi[joint_id].act(pin.SE3.Identity()).translation + body_com_local
    # print("data.oMi[joint_id].act(pin.SE3.Identity()).translation:", data.oMi[joint_id].act(pin.SE3.Identity()).translation.T)
    # print("body_com_world:", body_com_world.T)
    
    # 累加
    total_mass += mass
    weighted_com += mass * body_com_world

# 计算剩余刚体的质心
partial_com = weighted_com / total_mass
print("Partial CoM (ignoring legs):", partial_com.T)