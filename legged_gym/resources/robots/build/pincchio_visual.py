import pinocchio as pin
import numpy as np
import os
from pinocchio.utils import zero
import torch

# 载入机器人模型（请根据实际路径调整）
urdf_path = "/home/<USER>/Documents/simulation/legged_gym/resources/robots/phybot_v8/urdf/phybot_v8_26.urdf"
# urdf_path = "/home/<USER>/Documents/PhybotSofware/RobotModel/phybot/urdf/phybot_v6_floating2.urdf"
model = pin.buildModelFromUrdf(urdf_path)
data = model.createData()

# 打印模型基本信息
print("Model name:", model.name)
print("Number of joints:", model.njoints)
print("Number of bodies:", model.nbodies)
print("Number of configuration variables (nq):", model.nq)
print("Number of degrees of freedom (nv):", model.nv)

# 打印每个关节的信息
for idx in range(model.njoints):
    joint = model.joints[idx]
    joint_name = model.names[idx]
    print(f"Joint {idx}: {joint_name}, type: {joint.shortname()}, nq: {joint.nq}, nv: {joint.nv}")

# 将所有 q 置为零
q0 = zero(model.nq)

# 进行前向运动学计算
pin.forwardKinematics(model, data, q0)
pin.updateFramePlacements(model, data)

# # 打印每个关节的位姿（以 SE(3) 表示）
# print("\nJoint poses:")
# for i in range(model.njoints):
#     print(f"Joint {i} ({model.names[i]}):")
#     print(data.liMi[i])  # data.oMi[i] 是一个 SE3 对象，包含旋转矩阵和平移向量
#     print("-" * 50)

# print(f"CUDA 是否可用: {torch.cuda.is_available()}")
