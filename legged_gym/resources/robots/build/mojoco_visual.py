import time
import numpy as np
import mujoco
import mujoco.viewer

# 加载模型
m = mujoco.MjModel.from_xml_path('/home/<USER>/Documents/PhybotSofware/RobotModel/phybot_v8/xml/phybot_v8_26.xml')
# m = mujoco.MjModel.from_xml_path('/home/<USER>/Documents/PhybotSofware/RobotModel/phybot/urdf/phybot_v6_floating_whole2.xml')
d = mujoco.MjData(m)

print("qpos 的长度:", m.nq)
# base:0-5,left leg:6-12,right leg:13-19,waist:20-21,left arm:22-26,right arm:27-31
# 设置关节的初始位置（冻结在 0 位置）
# d.qpos[:3] = [0, 0, 1.2]
# d.qpos[3] = 1  
# d.qpos[4:] = 0  
# d.qpos[7:13] = [-0.824104, -0.145594, -0.379475, 1.37024, -0.619576, -0.00545543]
# # # d.qpos[7:13] = [-0.475537,-0.395696,-1.12344,0.0805315,-0.568457,1.3024]
# d.qpos[13:19] =[-0.824104, 0.145594, 0.379475, 1.37024, -0.619576, 0.00545543]
d.qpos[:] = 0  

# 构造整个系统的目标位置向量（长度 m.nq）
# 假设 base 部分占前 7 个自由度，其他 actuated 部分由你控制
q_desired = np.zeros(m.nq)
# 设置左腿目标位置：索引 7~12 (6 个自由度)
q_desired[7:13] = np.array([-0.824104, -0.145594, -0.379475, 1.37024, -0.619576, -0.00545543])
# q_desired[7:13] = np.array([-0.475537,-0.395696,-1.12344,0.0805315,-0.568457,1.3024])
# 设置右腿目标位置：索引 14~19 (6 个自由度)
q_desired[13:19] = np.array([-0.824104, 0.145594, 0.379475, 1.37024, -0.619576, 0.00545543])
# 其余关节（包括 base）保持 0

# 对于自由浮动的机器人:
# qpos 的 base 部分占 7 个自由度，而 qvel 的 base 部分只占 6 个自由度
base_qpos_dof = 7
base_qvel_dof = 6

# 可控关节的数量应等于 m.nu（actuator 数量），它等于 m.nq - base_qpos_dof，
# 同时 d.qvel[base_qvel_dof:] 的长度也应为 m.nu
actuated_dof = m.nu

# 设置 PD 控制器增益（这些参数需要根据具体情况调试）
Kp = np.ones(actuated_dof) * 1000  # 比例增益
Kd = np.ones(actuated_dof) * 200   # 微分增益

print("Joints in model:")
for i in range(m.njnt):
    joint_name = mujoco.mj_id2name(m, mujoco.mjtObj.mjOBJ_JOINT, i)
    print(f"Joint {i}: {joint_name}")
    
# print("Bodies in model:")
# for i in range(m.nbody):
#     body_name = mujoco.mj_id2name(m, mujoco.mjtObj.mjOBJ_BODY, i)
#     print(f"Body {i}: {body_name}")
m.vis.scale.jointlength = 1  # 增加这个值使轴更长
# 设置关节轴的宽度/粗细 (例如，设置为0.01米)
m.vis.scale.jointwidth = 0.2 # 增加这个值使轴更粗

# 启动可视化
with mujoco.viewer.launch_passive(m, d) as viewer:
    # viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_JOINT] = 1  # 显示关节坐标系
    # 获取所有几何体的 ID
    for i in range(m.ngeom):
        # 获取每个几何体的原始颜色 (rgba)
        rgba = m.geom_rgba[i]
        
        # 将透明度设置为 0.5 (半透明)
        rgba[3] = 1  # alpha (透明度) 设置为 0.5
        
        # 更新几何体的 rgba 值
        m.geom_rgba[i] = rgba

    # 设置可视化选项
    with viewer.lock():
        viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_JOINT] = True  # 显示关节转轴
        # viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_CONSTRAINT] = True  
        # viewer.opt.jointwidth = 5.0  # 调整关节转轴的粗细
        # viewer.opt.framewidth = 2.0   # 调整坐标系线宽
        # viewer.opt.axeslength = 0.2   # 调整坐标轴长度（部分版本可能不支持）
    # 关闭查看器前持续 60 秒
    start = time.time()
    while viewer.is_running() and time.time() - start > 0:
        step_start = time.time()
        
        # # 步进仿真
        # mujoco.mj_step(m, d)
        # d.qpos[:] = 0  
        # d.qpos[:2] = 0  
        # d.qpos[2] = 0.8
        # d.qpos[3] = 1  
        # d.qpos[4:7] = 0

        # d.qpos[7:13] = [-0.824104, -0.145594, -0.379475, 1.37024, -0.619576, -0.00545543]
        # d.qpos[14:20] = [-0.824104, 0.145594, 0.379475, 1.37024, -0.619576, 0.00545543]

        # # print(f"d.qpos[2]:{d.qpos[2]}")

        # # 读取 actuated 部分的当前状态
        # # 对于位置，我们从 d.qpos 中跳过 base 的 7 个自由度
        # qpos_actuated = d.qpos[base_qpos_dof:]
        # # 对于速度，则跳过 base 的 6 个自由度
        # qvel_actuated = d.qvel[base_qvel_dof:]
        # # 目标 actuated 部分也对应 q_desired 从索引 7 开始
        # q_desired_actuated = q_desired[base_qpos_dof:]
        
        # # 计算位置误差与速度误差（目标速度设为 0）
        # q_error = q_desired_actuated - qpos_actuated
        # qdot_error = -qvel_actuated
        
        # # 计算控制扭矩（PD 控制器）
        # tau = Kp * q_error + Kd * qdot_error
        
        # # 将计算出的控制信号赋值给 d.ctrl
        # d.ctrl[:] = tau
        
        # # 进行仿真步进，更新状态
        # mujoco.mj_step(m, d)
        

        # 修改查看器设置：显示骨架而非几何体
        # ith viewer.lock():Flag.mjVIS_BONE] = 1
        #     显示关节（坐标系）
            # viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_JOINT] = 1

        # 更新查看器
        viewer.sync()

        # 时间控制，避免步进过快
        time_until_next_step = m.opt.timestep - (time.time() - step_start)
        if time_until_next_step > 0:
            time.sleep(time_until_next_step)
            # 显示关节（坐标系）
            # viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_JOINT] = 1
            # viewer.opt.flags[mujoco.mjtVisFlag.mjVIS_BODYBVH] = 1

        # 更新查看器
        viewer.sync()

