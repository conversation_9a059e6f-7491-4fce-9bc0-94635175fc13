import numpy as np
import matplotlib.pyplot as plt
from urdfpy import URDF

from mpl_toolkits.mplot3d import Axes3D

custom_label_offsets = {
    "left_ankle_roll": np.array([0.1, 0, -0.05]),
    "right_ankle_roll": np.array([0.1, 0, -0.05]),
    "left_ankle_pitch": np.array([0.1, 0, 0.05]),
    "right_ankle_pitch": np.array([0.1, 0, 0.05])
}

def plot_frame(ax, T, length=0.05, name=None, name_offset=0.02, custom_offset_map=None):
    origin = T[:3, 3]
    R = T[:3, :3]
    colors = ['r', 'g', 'b']
    for i in range(3):
        axis = R[:, i] * length
        ax.quiver(
            origin[0], origin[1], origin[2],
            axis[0], axis[1], axis[2],
            color=colors[i], arrow_length_ratio=0.2
        )

    if name:
        offset_dir = np.array([1, 3, 1]) / np.sqrt(3)
        offset = offset_dir * name_offset

        # ✅ 检查是否有用户指定的额外偏移
        if custom_offset_map and name in custom_offset_map:
            offset += custom_offset_map[name]

        text_pos = origin + offset
        # ax.text(
        #     text_pos[0], text_pos[1], text_pos[2],
        #     name, fontsize=10, zorder=20,
        #     color='black'
        # )
        ax.text(
            text_pos[0], text_pos[1], text_pos[2],
            name,
            fontsize=10,
            color='black',
            ha='center', va='center',
            zorder=100,  # 尽量提高优先级
            bbox=dict(boxstyle='round,pad=0.3', facecolor='white', edgecolor='black', linewidth=0.5, alpha=0.5)
        )

def draw_axis_legend(ax, origin=np.array([0, 0, 0]), length=0.05):
    """
    在图的一角绘制坐标轴图例
    """
    x_axis = origin + np.array([length, 0, 0])
    y_axis = origin + np.array([0, length, 0])
    z_axis = origin + np.array([0, 0, length])

    ax.quiver(*origin, *(x_axis - origin), color='r', arrow_length_ratio=0.2)
    ax.quiver(*origin, *(y_axis - origin), color='g', arrow_length_ratio=0.2)
    ax.quiver(*origin, *(z_axis - origin), color='b', arrow_length_ratio=0.2)

    ax.text(*(x_axis + 0.005), 'X', color='r', fontsize=8, zorder=20)
    ax.text(*(y_axis + 0.005), 'Y', color='g', fontsize=8, zorder=20)
    ax.text(*(z_axis + 0.005), 'Z', color='b', fontsize=8, zorder=20)

    ax.text(*(origin + np.array([0.02, 0.02, 0.02])), 'base', color='k', fontsize=10, zorder=20)


def draw_joint_frames(robot: URDF):
    import matplotlib.pyplot as plt
    from mpl_toolkits.mplot3d import Axes3D
    import numpy as np

    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')

    # 构建 parent → joints 的映射表
    parent_to_joints = {}
    for joint in robot.joints:
        parent = joint.parent
        if parent not in parent_to_joints:
            parent_to_joints[parent] = []
        parent_to_joints[parent].append(joint)

    link_poses = {}
    base_link = robot.base_link.name
    link_poses[base_link] = np.eye(4)

    queue = [base_link]
    all_positions = []

    revolute_count = 0  # 计数 revolute joints

    while queue:
        current_link = queue.pop(0)
        current_pose = link_poses[current_link]

        for joint in parent_to_joints.get(current_link, []):            
            if joint.joint_type != 'revolute':
                continue
            child_link = joint.child  # 🔧 修复未定义变量
            T_joint = joint.origin
            T_child = current_pose @ T_joint
            link_poses[child_link] = T_child


            queue.append(child_link)  # ✅ 总是递归遍历所有 joint

            if joint.joint_type == 'revolute':
                revolute_count += 1
                all_positions.append(T_child[:3, 3])
                plot_frame(ax, T_child, name=joint.name, name_offset=0.05, custom_offset_map=custom_label_offsets)

                # ✅ 绘制关节旋转轴（以紫色表示）
                joint_axis_local = np.array(joint.axis)
                joint_axis_global = T_child[:3, :3] @ joint_axis_local  # 转为全局坐标
                origin = T_child[:3, 3]
                axis_len = 0.06
                ax.quiver(
                    origin[0], origin[1], origin[2],
                    joint_axis_global[0] * axis_len,
                    joint_axis_global[1] * axis_len,
                    joint_axis_global[2] * axis_len,
                    color='m', linewidth=2, arrow_length_ratio=0.3,
                    zorder=15
                )

    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_title('Revolute Joint Frames')

    # ✅ 用 Z 轴范围统一设置 XYZ 的显示范围
    if all_positions:
        all_positions = np.array(all_positions)
        center = all_positions.mean(axis=0)
        z_range = all_positions[:, 2].max() - all_positions[:, 2].min()
        z_range = max(z_range, 1e-3)
        r = z_range / 2

        ax.set_xlim(center[0] - r, center[0] + r)
        ax.set_ylim(center[1] - r, center[1] + r)
        ax.set_zlim(center[2] - r, center[2] + r)

    ax.set_box_aspect([1, 1, 1])  # 设置坐标轴等比例
    plt.tight_layout()

    draw_axis_legend(ax)  # ✅ 添加坐标轴图例

    print(f"Total revolute joints plotted: {revolute_count}")  # ✅ 打印数量

    ax.view_init(elev=10, azim=-20)

    plt.tight_layout()

    plt.show()



if __name__ == '__main__':
    robot = URDF.load("/home/<USER>/Documents/PhybotSofware/RobotModel/phybot_v8/urdf/phybot_v8_26.urdf")  # 替换为你的路径
    draw_joint_frames(robot)
