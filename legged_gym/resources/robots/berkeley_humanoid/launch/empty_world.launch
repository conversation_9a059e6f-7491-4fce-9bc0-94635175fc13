<launch>

    <!-- Name of the robot description parameter -->
    <arg name="description_name" default="legged_robot_description"/>
    <!-- Set robot description path -->
    <arg name="description_file" default="$(find berkeley_humanoid_description)/urdf/robot.urdf"/>

    <rosparam file="$(find berkeley_humanoid_description)/config/default.yaml" command="load"/>

    <!-- Load robot description -->
    <include file="$(find berkeley_humanoid_description)/launch/load.launch">
        <arg name="description_name" value="$(arg description_name)"/>
        <arg name="description_file" value="$(arg description_file)"/>
    </include>

    <include file="$(find gazebo_ros)/launch/empty_world.launch">
        <arg name="paused" value="true"/>
    </include>

    <!-- push robot_description to factory and spawn robot in gazebo -->
    <node name="spawn_urdf" pkg="gazebo_ros" type="spawn_model" clear_params="true"
          args="-param $(arg description_name) -urdf -model cal_hum
          -z 0.515
          -J LL_HR -0.071
          -J LL_HAA 0.103
          -J LL_HFE -0.463
          -J LL_KFE 0.983
          -J LL_FFE -0.350
          -J LL_FAA 0.126
          -J LR_HR  0.071
          -J LR_HAA -0.103
          -J LR_HFE -0.463
          -J LR_KFE 0.983
          -J LR_FFE -0.350
          -J LR_FAA -0.126
          " output="screen"/>
</launch>
