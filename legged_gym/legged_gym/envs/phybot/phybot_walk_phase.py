


from legged_gym import LEGGED_GYM_ROOT_DIR, envs
from time import time
from warnings import WarningMessage
import numpy as np
import os

from isaacgym.torch_utils import *
from isaacgym import gymtorch, gymapi, gymutil

import torch, torchvision

from legged_gym import LEGGED_GYM_ROOT_DIR, POSE_DIR
from legged_gym.envs.base.base_task import BaseTask
from legged_gym.envs.base.humanoid import Humanoid
# from .humanoid_config import HumanoidCfg
from .phybot_walk_phase_config import PhybotWalkPhaseCfg
from legged_gym.envs.base.legged_robot import euler_from_quaternion
from legged_gym.gym_utils.math import *


class PhybotWalkPhase(Humanoid):
    def __init__(self, cfg: PhybotWalkPhaseCfg, sim_params, physics_engine, sim_device, headless):
        self.cfg = cfg
        self.use_estimator = (self.cfg.env.n_priv != 0)
        super().__init__(cfg, sim_params, physics_engine, sim_device, headless)
        self.last_feet_z = 0.05
        self.episode_length = torch.zeros((self.num_envs), device=self.device)
        self.feet_height = torch.zeros((self.num_envs, 2), device=self.device)
        self.reset_idx(torch.tensor(range(self.num_envs), device=self.device))
        phase = self._get_phase()
        self.compute_ref_state()
    
    def _get_body_indices(self):
        upper_arm_names = [s for s in self.body_names if self.cfg.asset.upper_arm_name in s]
        lower_arm_names = [s for s in self.body_names if self.cfg.asset.lower_arm_name in s]
        torso_name = [s for s in self.body_names if self.cfg.asset.torso_name in s]
        self.torso_indices = torch.zeros(len(torso_name), dtype=torch.long, device=self.device,
                                                 requires_grad=False)
        for j in range(len(torso_name)):
            self.torso_indices[j] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0],
                                                                                  torso_name[j])
        self.upper_arm_indices = torch.zeros(len(upper_arm_names), dtype=torch.long, device=self.device,
                                                     requires_grad=False)
        for j in range(len(upper_arm_names)):
            self.upper_arm_indices[j] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0],
                                                                                upper_arm_names[j])
        self.lower_arm_indices = torch.zeros(len(lower_arm_names), dtype=torch.long, device=self.device,
                                                requires_grad=False)
        for j in range(len(lower_arm_names)):
            self.lower_arm_indices[j] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0],
                                                                                lower_arm_names[j])
        knee_names = [s for s in self.body_names if self.cfg.asset.shank_name in s]
        self.knee_indices = torch.zeros(len(knee_names), dtype=torch.long, device=self.device, requires_grad=False)
        for i in range(len(knee_names)):
            self.knee_indices[i] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0], knee_names[i])

    def compute_ref_state(self):
        phase = self._get_phase()
        _sin_pos_l = torch.sin(2 * torch.pi * phase)
        _sin_pos_r = torch.sin(2 * torch.pi * phase + torch.pi)
        sin_pos_l = _sin_pos_l.clone()
        sin_pos_r = _sin_pos_r.clone()
        self.ref_dof_pos = torch.zeros_like(self.dof_pos)
        scale_1 = self.cfg.rewards.target_joint_pos_scale
        scale_2 = 2 * scale_1
        # left foot stance phase set to default joint pos
        sin_pos_l[sin_pos_l > 0] = 0
        self.ref_dof_pos[:, 0] = sin_pos_l * scale_1
        self.ref_dof_pos[:, 3] = -sin_pos_l * scale_2
        self.ref_dof_pos[:, 4] = sin_pos_l * scale_1
        # right foot stance phase set to default joint pos
        sin_pos_r[sin_pos_r > 0] = 0
        self.ref_dof_pos[:, 7] = sin_pos_r * scale_1
        self.ref_dof_pos[:, 10] = -sin_pos_r * scale_2
        self.ref_dof_pos[:, 11] = sin_pos_r * scale_1
        # Double support phase
        indices = (torch.abs(sin_pos_l) < self.cfg.rewards.double_support_threshold) & (torch.abs(sin_pos_r) < self.cfg.rewards.double_support_threshold)
        self.ref_dof_pos[indices] = 0
        self.ref_dof_pos += self.default_dof_pos_all

        self.ref_action = 2 * self.ref_dof_pos

        # 在 compute_ref_state 中也可以打印关节角度
        if hasattr(self, 'ref_debug_counter'):
            self.ref_debug_counter += 1
        else:
            self.ref_debug_counter = 0

        if self.ref_debug_counter % 200 == 0:  # 每200步打印一次
            env_id = 0
            current_joints = self.dof_pos[env_id].cpu().numpy()
            ref_joints = self.ref_dof_pos[env_id].cpu().numpy()

            print(f"\n=== compute_ref_state 中的关节信息 (Step {self.ref_debug_counter}) ===")
            print(f"当前关节角度: {np.degrees(current_joints)}")
            print(f"参考关节角度: {np.degrees(ref_joints)}")
            print(f"相位: {phase[env_id].item():.4f}")
            print("=" * 60)

    def debug_print_joint_info(self, env_id=0, step_interval=100):
        """
        专门用于调试的关节信息打印方法

        Args:
            env_id: 要打印的环境ID（默认为0，即第一个环境）
            step_interval: 打印间隔步数
        """
        if not hasattr(self, 'debug_step_counter'):
            self.debug_step_counter = 0

        self.debug_step_counter += 1

        if self.debug_step_counter % step_interval == 0:
            # 获取关节信息
            joint_pos = self.dof_pos[env_id].cpu().numpy()
            joint_vel = self.dof_vel[env_id].cpu().numpy()
            joint_pos_deg = np.degrees(joint_pos)

            # 获取观测值
            obs = self.obs_buf[env_id].cpu().numpy()

            print(f"\n{'='*80}")
            print(f"调试信息 - 环境 {env_id} (Step {self.debug_step_counter})")
            print(f"{'='*80}")

            # 打印关节位置
            print("关节位置 (度):")
            for i, (pos_rad, pos_deg) in enumerate(zip(joint_pos, joint_pos_deg)):
                print(f"  关节 {i:2d}: {pos_rad:8.4f} rad ({pos_deg:8.2f}°)")

            # 打印关节速度
            print(f"\n关节速度 (rad/s): {joint_vel}")

            # 打印观测值的关键部分
            print(f"\n观测值维度: {len(obs)}")
            print(f"观测值前10个元素: {obs[:10]}")

            # 如果有参考状态，也打印出来
            if hasattr(self, 'ref_dof_pos'):
                ref_pos = self.ref_dof_pos[env_id].cpu().numpy()
                ref_pos_deg = np.degrees(ref_pos)
                print(f"\n参考关节位置 (度): {ref_pos_deg}")

                # 计算误差
                error = joint_pos - ref_pos
                error_deg = np.degrees(error)
                print(f"关节误差 (度): {error_deg}")

            print(f"{'='*80}\n")


    def compute_observations(self):
        # 调用父类的观测计算方法
        super().compute_observations()

        # 调用调试方法打印关节信息
        self.debug_print_joint_info(env_id=0, step_interval=100)

    # ======================================================================================================================
    # Reward functions
    # ======================================================================================================================
