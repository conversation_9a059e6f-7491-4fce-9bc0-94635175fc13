


from legged_gym import LEGGED_GYM_ROOT_DIR, envs
from time import time
from warnings import WarningMessage
import numpy as np
import os

from isaacgym.torch_utils import *
from isaacgym import gymtorch, gymapi, gymutil

import torch, torchvision

from legged_gym import LEGGED_GYM_ROOT_DIR, POSE_DIR
from legged_gym.envs.base.base_task import BaseTask
from legged_gym.envs.base.humanoid import Humanoid
# from .humanoid_config import HumanoidCfg
from .phybot_walk_phase_config import PhybotWalkPhaseCfg
from legged_gym.envs.base.legged_robot import euler_from_quaternion
from legged_gym.gym_utils.math import *


class PhybotWalkPhase(Humanoid):
    def __init__(self, cfg: PhybotWalkPhaseCfg, sim_params, physics_engine, sim_device, headless):
        self.cfg = cfg
        self.use_estimator = (self.cfg.env.n_priv != 0)
        super().__init__(cfg, sim_params, physics_engine, sim_device, headless)
        self.last_feet_z = 0.05
        self.episode_length = torch.zeros((self.num_envs), device=self.device)
        self.feet_height = torch.zeros((self.num_envs, 2), device=self.device)
        self.reset_idx(torch.tensor(range(self.num_envs), device=self.device))
        phase = self._get_phase()
        self.compute_ref_state()
    
    def _get_body_indices(self):
        upper_arm_names = [s for s in self.body_names if self.cfg.asset.upper_arm_name in s]
        lower_arm_names = [s for s in self.body_names if self.cfg.asset.lower_arm_name in s]
        torso_name = [s for s in self.body_names if self.cfg.asset.torso_name in s]
        self.torso_indices = torch.zeros(len(torso_name), dtype=torch.long, device=self.device,
                                                 requires_grad=False)
        for j in range(len(torso_name)):
            self.torso_indices[j] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0],
                                                                                  torso_name[j])
        self.upper_arm_indices = torch.zeros(len(upper_arm_names), dtype=torch.long, device=self.device,
                                                     requires_grad=False)
        for j in range(len(upper_arm_names)):
            self.upper_arm_indices[j] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0],
                                                                                upper_arm_names[j])
        self.lower_arm_indices = torch.zeros(len(lower_arm_names), dtype=torch.long, device=self.device,
                                                requires_grad=False)
        for j in range(len(lower_arm_names)):
            self.lower_arm_indices[j] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0],
                                                                                lower_arm_names[j])
        knee_names = [s for s in self.body_names if self.cfg.asset.shank_name in s]
        self.knee_indices = torch.zeros(len(knee_names), dtype=torch.long, device=self.device, requires_grad=False)
        for i in range(len(knee_names)):
            self.knee_indices[i] = self.gym.find_actor_rigid_body_handle(self.envs[0], self.actor_handles[0], knee_names[i])

    def compute_ref_state(self):
        phase = self._get_phase()
        _sin_pos_l = torch.sin(2 * torch.pi * phase)
        _sin_pos_r = torch.sin(2 * torch.pi * phase + torch.pi)
        sin_pos_l = _sin_pos_l.clone()
        sin_pos_r = _sin_pos_r.clone()
        self.ref_dof_pos = torch.zeros_like(self.dof_pos)
        scale_1 = self.cfg.rewards.target_joint_pos_scale
        scale_2 = 2 * scale_1
        # left foot stance phase set to default joint pos
        sin_pos_l[sin_pos_l > 0] = 0
        self.ref_dof_pos[:, 0] = sin_pos_l * scale_1
        self.ref_dof_pos[:, 3] = -sin_pos_l * scale_2
        self.ref_dof_pos[:, 4] = sin_pos_l * scale_1
        # right foot stance phase set to default joint pos
        sin_pos_r[sin_pos_r > 0] = 0
        self.ref_dof_pos[:, 7] = sin_pos_r * scale_1
        self.ref_dof_pos[:, 10] = -sin_pos_r * scale_2
        self.ref_dof_pos[:, 11] = sin_pos_r * scale_1
        # Double support phase
        indices = (torch.abs(sin_pos_l) < self.cfg.rewards.double_support_threshold) & (torch.abs(sin_pos_r) < self.cfg.rewards.double_support_threshold)
        self.ref_dof_pos[indices] = 0
        self.ref_dof_pos += self.default_dof_pos_all

        self.ref_action = 2 * self.ref_dof_pos

    def debug_print_joint_info(self, env_id=0, step_interval=100, print_obs=True, print_ref=True):
        """
        专门用于调试的关节信息打印方法

        Args:
            env_id (int): 要打印的环境ID（默认为0，即第一个环境）
            step_interval (int): 打印间隔步数
            print_obs (bool): 是否打印观测值信息
            print_ref (bool): 是否打印参考状态信息
        """
        if not hasattr(self, 'debug_step_counter'):
            self.debug_step_counter = 0

        self.debug_step_counter += 1

        if self.debug_step_counter % step_interval == 0:
            self._print_detailed_joint_info(env_id, print_obs, print_ref)

    def _print_detailed_joint_info(self, env_id, print_obs=True, print_ref=True):
        """打印详细的关节信息"""
        # 获取关节信息
        joint_pos = self.dof_pos[env_id].cpu().numpy()
        joint_vel = self.dof_vel[env_id].cpu().numpy()
        joint_pos_deg = np.degrees(joint_pos)
        joint_vel_deg = np.degrees(joint_vel)

        # 定义关节名称（根据PhyBot的实际关节结构）
        joint_names = [
            "L_hip_yaw", "L_hip_roll", "L_hip_pitch",     # 0-2: 左髋关节
            "L_knee", "L_ankle_pitch", "L_ankle_roll",    # 3-5: 左膝和踝关节
            "R_hip_yaw", "R_hip_roll", "R_hip_pitch",     # 6-8: 右髋关节
            "R_knee", "R_ankle_pitch", "R_ankle_roll",    # 9-11: 右膝和踝关节
            "waist_yaw", "waist_pitch", "waist_roll",     # 12-14: 腰部关节
            "L_shoulder_pitch", "L_shoulder_roll", "L_shoulder_yaw",  # 15-17: 左肩关节
            "L_elbow", "L_wrist_yaw", "L_wrist_pitch",    # 18-20: 左臂关节
            "R_shoulder_pitch", "R_shoulder_roll", "R_shoulder_yaw",  # 21-23: 右肩关节
            "R_elbow", "R_wrist_yaw", "R_wrist_pitch"     # 24-26: 右臂关节
        ]

        # 确保关节名称数量匹配
        if len(joint_names) > len(joint_pos):
            joint_names = joint_names[:len(joint_pos)]
        elif len(joint_names) < len(joint_pos):
            joint_names.extend([f"joint_{i}" for i in range(len(joint_names), len(joint_pos))])

        print(f"\n{'='*100}")
        print(f"PhyBot 调试信息 - 环境 {env_id} (Step {self.debug_step_counter})")
        print(f"{'='*100}")

        # 打印关节位置和速度
        print("关节状态:")
        print(f"{'序号':<4} {'关节名称':<18} {'位置(rad)':<12} {'位置(度)':<12} {'速度(rad/s)':<15} {'速度(度/s)':<12}")
        print("-" * 100)
        for i, name in enumerate(joint_names):
            print(f"{i:<4} {name:<18} {joint_pos[i]:<12.4f} {joint_pos_deg[i]:<12.2f} "
                  f"{joint_vel[i]:<15.4f} {joint_vel_deg[i]:<12.2f}")

        # 打印腿部关节的详细信息
        print(f"\n{'='*60}")
        print("腿部关节详细信息:")
        print(f"{'='*60}")

        # 左腿
        print("左腿关节 (度):")
        left_leg_indices = [0, 1, 2, 3, 4, 5]  # L_hip_yaw 到 L_ankle_roll
        for i in left_leg_indices:
            if i < len(joint_names):
                print(f"  {joint_names[i]:<15}: {joint_pos_deg[i]:8.2f}°")

        # 右腿
        print("\n右腿关节 (度):")
        right_leg_indices = [6, 7, 8, 9, 10, 11]  # R_hip_yaw 到 R_ankle_roll
        for i in right_leg_indices:
            if i < len(joint_names):
                print(f"  {joint_names[i]:<15}: {joint_pos_deg[i]:8.2f}°")

        # 打印参考状态信息
        if print_ref and hasattr(self, 'ref_dof_pos'):
            ref_pos = self.ref_dof_pos[env_id].cpu().numpy()
            ref_pos_deg = np.degrees(ref_pos)

            print(f"\n{'='*60}")
            print("参考状态信息:")
            print(f"{'='*60}")

            # 计算误差
            error = joint_pos - ref_pos
            error_deg = np.degrees(error)

            print(f"{'关节名称':<18} {'当前(度)':<12} {'参考(度)':<12} {'误差(度)':<12}")
            print("-" * 60)
            for i, name in enumerate(joint_names):
                print(f"{name:<18} {joint_pos_deg[i]:<12.2f} {ref_pos_deg[i]:<12.2f} {error_deg[i]:<12.2f}")

            # 打印相位信息
            if hasattr(self, '_get_phase'):
                phase = self._get_phase()[env_id].item()
                print(f"\n步态相位: {phase:.4f}")

        # 打印观测值信息
        if print_obs and hasattr(self, 'obs_buf'):
            obs = self.obs_buf[env_id].cpu().numpy()
            print(f"\n{'='*60}")
            print("观测值信息:")
            print(f"{'='*60}")
            print(f"观测值维度: {len(obs)}")
            print(f"观测值范围: [{obs.min():.4f}, {obs.max():.4f}]")

            # 根据观测值结构打印关键部分
            print(f"\n观测值前10个元素: {obs[:10]}")
            if len(obs) > 10:
                print(f"观测值后10个元素: {obs[-10:]}")

        # 打印基础状态信息
        if hasattr(self, 'base_lin_vel') and hasattr(self, 'base_ang_vel'):
            base_lin_vel = self.base_lin_vel[env_id].cpu().numpy()
            base_ang_vel = self.base_ang_vel[env_id].cpu().numpy()

            print(f"\n{'='*60}")
            print("基础状态信息:")
            print(f"{'='*60}")
            print(f"线速度 (m/s): [{base_lin_vel[0]:.3f}, {base_lin_vel[1]:.3f}, {base_lin_vel[2]:.3f}]")
            print(f"角速度 (rad/s): [{base_ang_vel[0]:.3f}, {base_ang_vel[1]:.3f}, {base_ang_vel[2]:.3f}]")

            if hasattr(self, 'roll') and hasattr(self, 'pitch') and hasattr(self, 'yaw'):
                roll = self.roll[env_id].item()
                pitch = self.pitch[env_id].item()
                yaw = self.yaw[env_id].item()
                print(f"姿态角 (度): Roll={np.degrees(roll):.2f}, Pitch={np.degrees(pitch):.2f}, Yaw={np.degrees(yaw):.2f}")

        print(f"{'='*100}\n")

    def debug_print_specific_joints(self, joint_indices, env_id=0, step_interval=50):
        """
        打印特定关节的信息

        Args:
            joint_indices (list): 要打印的关节索引列表
            env_id (int): 环境ID
            step_interval (int): 打印间隔
        """
        if not hasattr(self, 'specific_debug_counter'):
            self.specific_debug_counter = 0

        self.specific_debug_counter += 1

        if self.specific_debug_counter % step_interval == 0:
            joint_pos = self.dof_pos[env_id].cpu().numpy()
            joint_vel = self.dof_vel[env_id].cpu().numpy()

            print(f"\n=== 特定关节调试 (Step {self.specific_debug_counter}) ===")
            for idx in joint_indices:
                if idx < len(joint_pos):
                    print(f"关节 {idx}: 位置={joint_pos[idx]:.4f}rad ({np.degrees(joint_pos[idx]):.2f}°), "
                          f"速度={joint_vel[idx]:.4f}rad/s")
            print("=" * 50)


    def compute_observations(self):
        """重写观测计算方法，添加调试功能"""
        # 调用父类的观测计算方法
        super().compute_observations()

        # 调用调试方法（可以根据需要调整参数）
        self.debug_print_joint_info(env_id=0, step_interval=100, print_obs=True, print_ref=True)

        # 如果只想看腿部关节，可以使用这个方法
        # leg_joint_indices = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]  # 腿部关节
        # self.debug_print_specific_joints(leg_joint_indices, env_id=0, step_interval=200)

    # ======================================================================================================================
    # Reward functions
    # ======================================================================================================================
