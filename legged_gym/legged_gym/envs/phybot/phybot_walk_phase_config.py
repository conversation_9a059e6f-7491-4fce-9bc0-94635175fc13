from legged_gym.envs.base.humanoid_config import HumanoidCfg, HumanoidCfgPPO

class PhybotWalkPhaseCfg(HumanoidCfg):
    class env(HumanoidCfg.env):
        num_envs = 4096
        num_actions = 26
        n_priv = 0
        n_proprio = 2 + 3 + 3 + 2 + 3*num_actions
        n_priv_latent = 4 + 1 + 2*num_actions + 3
        history_len = 10
        
        num_observations = n_proprio + n_priv_latent + history_len*n_proprio + n_priv

        num_privileged_obs = None

        env_spacing = 3.  # not used with heightfields/trimeshes 
        send_timeouts = True # send time out information to the algorithm
        episode_length_s = 10
        
        randomize_start_pos = True
        randomize_start_yaw = True
        
        history_encoding = True
        contact_buf_len = 10
        
        normalize_obs = True
    
    class terrain(HumanoidCfg.terrain):
        mesh_type = 'trimesh'
        height = [0, 0.04]
        horizontal_scale = 0.1
    
    class init_state(HumanoidCfg.init_state):
        pos = [0, 0, 0.95]
        default_joint_angles = {
            'left_hip_pitch': -0.555,
            'left_hip_roll': -0.068,
            'left_hip_yaw': -0.266,
            'left_knee': 0.878,
            'left_ankle_pitch': -0.385,
            'left_ankle_roll': 0.0,
            'left_toe': 0.0,
            
            'right_hip_pitch': -0.555,
            'right_hip_roll': 0.068,
            'right_hip_yaw': 0.266,
            'right_knee': 0.878,
            'right_ankle_pitch': -0.385,
            'right_ankle_roll': 0.0,
            'right_toe': 0.0,


            'waist_yaw': 0.0,
            'waist_roll': 0.0,
            
            'left_shoulder_pitch': 0.0,
            'left_shoulder_roll': 0.0,
            'left_shoulder_yaw': 0.0,
            'left_elbow_pitch': 0.0,
            'left_elbow_yaw': 0.0,
            
            'right_shoulder_pitch': 0.0,
            'right_shoulder_roll': 0.0,
            'right_shoulder_yaw': 0.0,
            'right_elbow_pitch': 0.0,
            'right_elbow_yaw': 0.0,
        }
    
    class control(HumanoidCfg.control):
        stiffness = {
            'hip_yaw': 300,
            'hip_roll': 300,
            'hip_pitch': 200,
            'knee': 300,
            'ankle': 20,
            'toe': 5,
            'waist': 150,
            'shoulder': 40,
            'elbow': 40,
        }
        damping = {
            'hip_yaw': 20,
            'hip_roll': 20,
            'hip_pitch': 10,
            'knee': 20,
            'ankle': 4,
            'toe': 1,
            'waist': 5,
            'shoulder': 1,
            'elbow': 1,
        }
        
        action_scale = 0.5
        decimation = 10
    
    class sim(HumanoidCfg.sim):
        dt = 0.001
        
    class normalization(HumanoidCfg.normalization):
        clip_actions = 5
    
    class asset(HumanoidCfg.asset):
        # file = '{LEGGED_GYM_ROOT_DIR}/resources/robots/g1/g1-nohand.urdf'
        file = '{LEGGED_GYM_ROOT_DIR}/resources/robots/phybot_v8/urdf/phybot_mark2.urdf'
        # for both joint and link name
        torso_name: str = 'torso'  # humanoid pelvis part
        chest_name: str = 'torso'  # humanoid chest part
        # forehead_name: str = 'head_link'  # humanoid head part

        waist_name: str = 'waist_yaw'

        # for link name
        thigh_name: str = 'hip_roll'
        shank_name: str = 'knee'
        foot_name: str = 'ankle_roll'  # foot_pitch is not used
        upper_arm_name: str = 'shoulder_roll'
        lower_arm_name: str = 'elbow_pitch'
        # hand_name: str = 'hand'

        # for joint name
        hip_name: str = 'hip'
        hip_roll_name: str = 'hip_roll_joint'
        hip_yaw_name: str = 'hip_yaw_joint'
        hip_pitch_name: str = 'hip_pitch_joint'
        knee_name: str = 'knee_joint'
        ankle_name: str = 'ankle'
        ankle_pitch_name: str = 'ankle_pitch_joint'
        shoulder_name: str = 'shoulder'
        shoulder_pitch_name: str = 'shoulder_pitch_joint'
        shoulder_roll_name: str = 'shoulder_roll_joint'
        shoulder_yaw_name: str = 'shoulder_yaw_joint'
        elbow_name: str = 'elbow_pitch_joint'

        feet_bodies = ['left_ankle_roll', 'right_ankle_roll']
        n_lower_body_dofs: int = 14

        penalize_contacts_on = ["shoulder", "elbow", "hip"]
        terminate_after_contacts_on = ['torso']
    
    class rewards(HumanoidCfg.rewards):
        # 定义需要计算的惩罚项（负奖励）列表
        regularization_names = [
                        "dof_error",           # 所有关节位置与默认位置的误差
                        "dof_error_upper",     # 上半身关节位置与默认位置的误差
                        "feet_stumble",        # 脚部绊倒惩罚
                        "feet_contact_forces", # 脚部接触力过大的惩罚
                        "lin_vel_z",           # 垂直方向线速度惩罚（防止跳跃）
                        "ang_vel_xy",          # 水平方向角速度惩罚（防止倾倒）
                        "orientation",         # 身体方向偏离直立的惩罚
                        "dof_pos_limits",      # 关节位置超出限制的惩罚
                        "dof_torque_limits",   # 关节扭矩超出限制的惩罚
                        "collision",           # 碰撞惩罚
                        "torque_penalty",      # 总扭矩使用的惩罚（鼓励能量效率）
                        ]
        # 惩罚项的整体缩放系数
        regularization_scale = 1.0
        # 惩罚项缩放系数的范围，用于课程学习
        regularization_scale_range = [0.8,2.0]
        # 是否启用惩罚项缩放系数的课程学习
        regularization_scale_curriculum = True
        # 惩罚项缩放系数的变化率
        regularization_scale_gamma = 0.0001
        
        class scales:
            # 关节位置跟踪奖励权重
            joint_pos = 1.6
            # 脚部离地高度奖励权重
            feet_clearance = 1.
            # 脚部接触数量奖励权重（步态相位）
            feet_contact_number = 1.2

            # 脚部空中时间奖励权重
            feet_air_time = 1.
            # 脚部滑动惩罚权重
            foot_slip = -0.1
            # 脚部距离奖励权重
            feet_distance = 0.2
            # 膝盖距离奖励权重
            knee_distance = 0.2

            # 线速度跟踪奖励权重（指数形式）
            tracking_lin_vel_exp = 1.875
            # 角速度跟踪奖励权重
            tracking_ang_vel = 2.0

            # 存活奖励权重
            alive = 2.0
            # 关节位置误差惩罚权重
            dof_error = -0.15
            # 上半身关节位置误差惩罚权重
            dof_error_upper = -0.2
            # 脚部绊倒惩罚权重
            feet_stumble = -1.25
            # 脚部接触力过大惩罚权重
            feet_contact_forces = -5e-4
            
            # 垂直方向线速度惩罚权重
            lin_vel_z = -1.0
            # 水平方向角速度惩罚权重
            ang_vel_xy = -0.1
            # 身体方向偏离直立惩罚权重
            orientation = -1.0
            
            # 碰撞惩罚权重
            collision = -10.0
            
            # 关节位置超出限制惩罚权重
            dof_pos_limits = -10
            # 关节扭矩超出限制惩罚权重
            dof_torque_limits = -1.0
            # 总扭矩使用惩罚权重
            torque_penalty = -6e-7

        # 脚部最小理想距离
        min_dist = 0.2
        # 脚部最大理想距离
        max_dist = 0.5
        # 膝盖最大理想距离
        max_knee_dist = 0.5
        # 目标关节位置缩放系数
        target_joint_pos_scale = 0.17
        # 目标脚部离地高度
        target_feet_height = 0.1
        # 步态周期时间（秒）
        cycle_time = 0.64
        # 双支撑阶段阈值
        double_support_threshold = 0.1
        # 是否只使用正向奖励
        only_positive_rewards = False
        # 速度跟踪的高斯分布标准差
        tracking_sigma = 0.2
        # 角速度跟踪的高斯分布标准差
        tracking_sigma_ang = 0.125
        # 最大允许接触力，超过此值将被惩罚
        max_contact_force = 1000
        # 终止高度，低于此高度认为机器人跌倒
        termination_height = 0.3
    
    class domain_rand:
        domain_rand_general = True # manually set this, setting from parser does not work;
        
        randomize_gravity = (True and domain_rand_general)
        gravity_rand_interval_s = 4
        gravity_range = (-0.1, 0.1)
        
        randomize_friction = (True and domain_rand_general)
        friction_range = [0.6, 2.]
        
        randomize_base_mass = (True and domain_rand_general)
        added_mass_range = [-3., 3]
        
        randomize_base_com = (True and domain_rand_general)
        added_com_range = [-0.05, 0.05]
        
        push_robots = (True and domain_rand_general)
        push_interval_s = 4
        max_push_vel_xy = 1.0

        randomize_motor = (True and domain_rand_general)
        motor_strength_range = [0.8, 1.2]

        action_delay = (True and domain_rand_general)
        action_buf_len = 8
    
    class noise(HumanoidCfg.noise):
        add_noise = True
        noise_increasing_steps = 5000
        class noise_scales:
            dof_pos = 0.01
            dof_vel = 0.1
            lin_vel = 0.1
            ang_vel = 0.05
            gravity = 0.05
            imu = 0.05
        
    class commands:
        curriculum = False
        num_commands = 3
        resampling_time = 3. # time before command are changed[s]

        ang_vel_clip = 0.1
        lin_vel_clip = 0.1

        class ranges:
            lin_vel_x = [0., 0.8] # min max [m/s]
            lin_vel_y = [-0.3, 0.3]
            ang_vel_yaw = [-0.6, 0.6]    # min max [rad/s]

class PhybotWalkPhaseCfgPPO(HumanoidCfgPPO):
    seed = 1
    class runner(HumanoidCfgPPO.runner):
        policy_class_name = 'ActorCriticRMA'
        algorithm_class_name = 'PPORMA'
        runner_class_name = 'OnPolicyRunner'
        max_iterations = 20001 # number of policy updates

        # logging
        save_interval = 100 # check for potential saves every this many iterations
        experiment_name = 'test'
        run_name = ''
        # load and resume
        resume = False
        load_run = -1 # -1 = last run
        checkpoint = -1 # -1 = last saved model
        resume_path = None # updated from load_run and chkpt
    
    class policy(HumanoidCfgPPO.policy):
        action_std = [0.3, 0.3, 0.3, 0.4, 0.2, 0.2, 0.2] * 2 + [0.1] * 2 + [0.2] * 10
        
    class algorithm(HumanoidCfgPPO.algorithm):
        grad_penalty_coef_schedule = [0.002, 0.002, 700, 1000]
        
